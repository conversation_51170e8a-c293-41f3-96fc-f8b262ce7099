import { notification, TreeProps } from 'antd';
import { useCallback, useState } from 'react';
import { WorkGroupEPCConfig } from 'widgets/WorkGroupEPC';
import { EpcPermissions } from 'widgets/WorkGroupEPC/types';
import { newlazyTree, TreeActions } from 'features/NewLazyTree';
import { onDragOver, handleLazyTreeDnD, OnDragOver } from 'shared/lib';
import { useAppSelector } from 'shared/model';

type UseTreeDragNDrop = [
  draggable: boolean,
  dropForbidden: boolean,
  {
    allowDrop: NonNullable<TreeProps<TreeElement>['allowDrop']>;
    onDragOver: OnDragOver;
    onDragStart: (info: { node: TreeElement }) => void;
    onDrop: TreeProps<TreeElement>['onDrop'];
  },
];

export const useTreeDragNDrop = ({
  epcPermissions,
  moveNode,
  isDraggable = true,
}: {
  epcPermissions: EpcPermissions;
  isDraggable: boolean;
  moveNode: TreeActions['moveNode'];
}): UseTreeDragNDrop => {
  const [isDropForbidden, setDropForbidden] = useState(true);
  const { EPC_TREE_KEY } = WorkGroupEPCConfig;

  const entities = useAppSelector((state) =>
    newlazyTree.selectors.entitiesSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );

  const onDrop: TreeProps<TreeElement>['onDrop'] = useCallback(
    (info) => {
      handleLazyTreeDnD(entities, moveNode)?.(info);
    },
    [entities, moveNode],
  );

  const onDragStart = useCallback(
    (info: { node: TreeElement }) => {
      const parentNode = entities[info.node.parent || ''];
      if (parentNode?.isFixed === 2 && !info.node.isDirectory) {
        notification.warn({
          message: 'Нельзя переносить файлы из системной директории.',
        });
      }
    },
    [entities],
  );

  const allowDrop: NonNullable<TreeProps<TreeElement>['allowDrop']> =
    useCallback(
      ({ dropNode, dragNode, dropPosition }) => {
        const targetNode =
          dropPosition === 0
            ? entities[dropNode.itemId!]
            : (entities[dropNode.parent!] as TreeElement | undefined);

        if (dragNode.isDirectory) {
          return false;
        }

        const parentDragNode = entities[dragNode.parent!];
        if (parentDragNode?.isFixed === 2) {
          return false;
        }

        if (!targetNode?.isDirectory) {
          return false;
        }

        if (dragNode.parent === targetNode.itemId) {
          setDropForbidden(true);
          return true;
        }

        if (targetNode?.isFixed === 2) {
          setDropForbidden(true);
          return true;
        }

        const hasDuplicate = targetNode?.childrenIds
          ?.map((id) => entities[id]?.title)
          .some((title) => title && title === dragNode.title);

        if (hasDuplicate) {
          setDropForbidden(true);
          return true;
        }

        setDropForbidden(false);
        return true;
      },
      [entities, setDropForbidden],
    );

  const draggable = epcPermissions.canCopyFiles && isDraggable;

  return [
    draggable,
    isDropForbidden,
    {
      onDrop,
      onDragStart,
      allowDrop,
      onDragOver,
    },
  ];
};
