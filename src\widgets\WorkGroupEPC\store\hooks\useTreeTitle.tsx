import { useCallback } from 'react';
import {
  EpcPermissions,
  FilesVisibility,
  ToggleEPCPopup,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
// eslint-disable-next-line import/no-internal-modules
import { NodeWithButtons } from 'widgets/WorkGroupEPC/ui/NodeWithButtons';
import { TreeActions } from 'features/NewLazyTree';
import { renderTreeTitle, useCreateSliceActions } from 'shared/model';

type UseTreeTitle = {
  deleteNode: TreeActions['deleteNode'];
  epcPermissions: EpcPermissions;
  findedItemId: string;
  refetchNode: TreeActions['refetchNode'];
  saveStatus: FilesVisibility;
  togglePopup: ToggleEPCPopup;
};

export const useTreeTitle = ({
  deleteNode,
  refetchNode,
  saveStatus,
  findedItemId,
  togglePopup,
  epcPermissions,
}: UseTreeTitle): ((node: TreeElement) => JSX.Element) => {
  const { handleUpdateLinked } = useCreateSliceActions(
    WorkGroupEPCStore.reducers.slice.actions,
  );
  const renderTitle = useCallback(
    (node: TreeElement) => (
      <NodeWithButtons
        deleteNode={deleteNode}
        refetchNode={refetchNode}
        saveStatus={saveStatus}
        node={node}
        title={
          <div>
            {renderTreeTitle(
              node,
              node.key === findedItemId,
              node.hasLinked,
              false, // TODO Переписать renderTreeTitle
              true,
            )}
          </div>
        }
        togglePopup={togglePopup}
        handleUpdateLinked={handleUpdateLinked}
        epcPermissions={epcPermissions}
      />
    ),
    [
      deleteNode,
      refetchNode,
      saveStatus,
      findedItemId,
      togglePopup,
      handleUpdateLinked,
      epcPermissions,
    ],
  );
  return renderTitle;
};
