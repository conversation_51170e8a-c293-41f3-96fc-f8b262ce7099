// DownloadButton.tsx
import { DownloadOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import axios from 'axios';
import { memo } from 'react';
import { permissionsConfig } from 'entities/Permissions';
import { apiUrls, appInstance } from 'shared/api';
import { appErrorNotification, downloadFile } from 'shared/lib';
import { normalizeFileName } from 'shared/model';

interface DownloadButtonProps {
  canDownload: boolean;
  fileNetId: string;
}

export const DownloadButton = memo(
  ({ canDownload, fileNetId }: DownloadButtonProps) => {
    const handleDownload = async (
      e: React.MouseEvent<HTMLButtonElement>,
    ): Promise<void> => {
      e.stopPropagation();

      if (fileNetId) {
        try {
          const res = await appInstance.get(
            apiUrls.workGroup.fileData.downloadFileById(fileNetId),
            {
              responseType: 'blob',
            },
          );
          downloadFile(res.data, normalizeFileName(res.headers));
        } catch (err) {
          if (axios.isAxiosError(err)) {
            appErrorNotification(
              err.response?.data?.error || 'Ошибка загрузки файла',
              err,
            );
          }
          throw err;
        }
      }
    };

    return (
      <Tooltip
        title={
          canDownload
            ? 'Скачать файл'
            : permissionsConfig.warnMessages.noPermissionsDefault('скачивание')
        }
      >
        <Button
          disabled={!canDownload}
          type="dashed"
          size="small"
          icon={<DownloadOutlined />}
          onClick={handleDownload}
        />
      </Tooltip>
    );
  },
);
