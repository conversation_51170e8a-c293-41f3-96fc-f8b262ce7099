// DownloadWithSignButton.tsx
import { SafetyOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import axios from 'axios';
import { memo } from 'react';
import { permissionsConfig } from 'entities/Permissions';
import { apiUrls, appInstance } from 'shared/api';
import { appErrorNotification, downloadFile } from 'shared/lib';
import { normalizeFileName } from 'shared/model';

interface DownloadWithSignButtonProps {
  canDownload: boolean;
  fileNetId: string;
}

export const DownloadWithSignButton = memo(
  ({ canDownload, fileNetId }: DownloadWithSignButtonProps) => {
    const handleDownloadWithSign = async (
      e: React.MouseEvent<HTMLButtonElement>,
    ): Promise<void> => {
      e.stopPropagation();

      try {
        const res = await appInstance.get(
          apiUrls.workGroup.fileData.downloadFileByIdWithSign(fileNetId),
          {
            responseType: 'blob',
          },
        );
        downloadFile(res.data, normalizeFileName(res.headers));
      } catch (err) {
        if (axios.isAxiosError(err)) {
          appErrorNotification(
            err.response?.data?.error || 'Ошибка загрузки файла',
            err,
          );
        }
        throw err;
      }
    };

    return (
      <Tooltip
        title={
          canDownload
            ? permissionsConfig.warnMessages.noPermissionsDefault('скачивание')
            : 'Скачать файл с подписью'
        }
      >
        <Button
          disabled={!canDownload}
          type="dashed"
          size="small"
          icon={<SafetyOutlined />}
          onClick={handleDownloadWithSign}
        />
      </Tooltip>
    );
  },
);
