import { DeleteTwoTone } from '@ant-design/icons';
import { Button, Tooltip, notification } from 'antd';
import { memo } from 'react';
import { TreeActions } from 'features/NewLazyTree';
import {
  fileDeletionHintsByBinding,
  FileDeletionHintsByBindingKeys,
} from 'shared/config';

import { createConfirmModal } from 'shared/model';

interface DeleteButtonProps {
  deleteNode: TreeActions['deleteNode'];
  node: TreeElement;
}

export const DeleteButton = memo(({ node, deleteNode }: DeleteButtonProps) => {
  const handleDelete = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.stopPropagation();
    createConfirmModal({
      isConfirmDanger: true,
      closeOnEscape: false,
      shouldCloseOnBackdropClick: false,
      title: 'Внимание',
      message: `Вы действительно хотите удалить "${node.title}"?`,
      closeOnReject: true,
      onConfirm: async () => {
        if (node.fileNetId) {
          await deleteNode({
            itemId: node.itemId!,
            fileNetId: node.fileNetId!,
            parentId: node.parent!,
          });
          notification.success({
            message: `Файл "${node.title}" успешно удален`,
          });
        }
      },
    });
  };

  return (
    <Tooltip
      title={
        node.isRemovable
          ? 'Удалить файл'
          : Object.keys(fileDeletionHintsByBinding)
              .reduce(
                (acc, key) => {
                  if (key in node && node[key as keyof TreeElement]) {
                    return [
                      ...acc,
                      fileDeletionHintsByBinding[
                        key as FileDeletionHintsByBindingKeys
                      ] || '',
                    ];
                  }
                  return acc;
                },
                ['Невозможно удалить файл'],
              )
              .filter((hint) => hint)
              .join('. ')
      }
    >
      <Button
        disabled={!node.isRemovable}
        type="dashed"
        size="small"
        icon={<DeleteTwoTone twoToneColor="#FF4D4F" />}
        onClick={handleDelete}
      />
    </Tooltip>
  );
});
