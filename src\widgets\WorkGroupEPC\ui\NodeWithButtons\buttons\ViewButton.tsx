import { EyeOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import { memo } from 'react';
import { appInstance, apiUrls } from 'shared/api';
import {
  appErrorNotification,
  createNewWindowForApps,
  guessNoticeMessage,
} from 'shared/lib';

interface ViewButtonProps {
  canView: boolean;
  fileNetId: string;
}

export const ViewButton = memo(({ canView, fileNetId }: ViewButtonProps) => {
  const handleView = async (
    e: React.MouseEvent<HTMLButtonElement>,
  ): Promise<void> => {
    e.stopPropagation();

    if (fileNetId) {
      try {
        const res = await appInstance.get<{
          data: string;
          success: boolean;
        }>(apiUrls.workGroup.fileData.fileViewer(fileNetId));
        guessNoticeMessage('viewer');
        createNewWindowForApps(res.data.data);
      } catch (error) {
        appErrorNotification(
          'Ошибка при открытии визуализатора',
          error as AppError,
        );
      }
    }
  };

  return (
    <Tooltip title="Просмотр в визуализаторе">
      <Button
        disabled={!canView}
        type="dashed"
        size="small"
        icon={<EyeOutlined />}
        onClick={handleView}
      />
    </Tooltip>
  );
});
