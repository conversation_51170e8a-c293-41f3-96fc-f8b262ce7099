import { BlockOutlined } from '@ant-design/icons';
import { Button, Tooltip, notification } from 'antd';
import { memo } from 'react';
import { FilesVisibility, WorkGroupEPCConfig } from 'widgets/WorkGroupEPC';
import { newlazyTree } from 'features/NewLazyTree';
import { permissionsConfig } from 'entities/Permissions';
import { appErrorNotification } from 'shared/lib';
import { createConfirmModal, useCreateSliceActions } from 'shared/model';

interface ChangeVisibilityButtonProps {
  cabinetOnly: boolean;
  canEdit: boolean;
  isDirectory: boolean;
  itemId: string;
  saveStatus: FilesVisibility;
  title: string;
}

export const ChangeVisibilityButton = memo(
  ({
    cabinetOnly,
    isDirectory,
    title,
    itemId,
    canEdit,
    saveStatus,
  }: ChangeVisibilityButtonProps) => {
    const { updateNodes } = useCreateSliceActions(
      newlazyTree.reducers.slice.actions,
    );

    const handleChangeVisibility = (
      e: React.MouseEvent<HTMLButtonElement>,
    ): void => {
      e.stopPropagation();
      createConfirmModal({
        title: 'Внимание',
        shouldCloseOnBackdropClick: false,
        closeOnEscape: false,
        message: !cabinetOnly
          ? `Вы действительно хотите скрыть ${
              isDirectory ? 'директорию' : 'файл'
            } для Контроля выполнения?\n` +
            `${isDirectory ? 'Директория' : 'Файл'} будет доступ${
              isDirectory ? 'на' : 'ен'
            } в режиме "только для КРГ".`
          : `${isDirectory ? 'Директория' : 'Файл'} доступ${
              isDirectory ? 'на' : 'ен'
            } в режиме "только для КРГ".\n` +
            `Вы действительно хотите отображать ${
              isDirectory ? 'директорию' : 'файл'
            } в Контроле выполнения?`,
        onConfirm: async () => {
          try {
            if (itemId) {
              if (isDirectory) {
                await saveStatus([], [itemId], !cabinetOnly);
              } else {
                await saveStatus([itemId], [], !cabinetOnly);
              }
              updateNodes({
                treeKey: WorkGroupEPCConfig.EPC_TREE_KEY,
                itemIds: [itemId],
                updateCallback: (node: TreeElement) => {
                  node.cabinetOnly = !node.cabinetOnly;
                },
              });
              notification.success({
                message: `Доступность ${
                  isDirectory ? 'директории' : 'файла'
                } "${title}" успешно изменена`,
              });
            }
          } catch (err) {
            appErrorNotification(
              'Произошла ошибка изменения видимости',
              err as AppError,
            );
          }
        },
      });
    };

    return (
      <Tooltip
        title={
          !canEdit
            ? permissionsConfig.warnMessages.noPermissionsDefault(
                'изменение видимости',
              )
            : `Изменить видимость ${isDirectory ? 'директории' : 'файла'}`
        }
      >
        <Button
          disabled={!canEdit}
          type="dashed"
          size="small"
          icon={<BlockOutlined />}
          onClick={handleChangeVisibility}
        />
      </Tooltip>
    );
  },
);
