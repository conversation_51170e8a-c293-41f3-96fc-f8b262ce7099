import { useCallback, useRef } from 'react';
import { newlazyTree } from 'features/NewLazyTree';
import { apiUrls } from 'shared/api';
import { useAppDispatch, useCreateSliceActions } from 'shared/model';
import { WorkGroupEPCConfig } from '../..';
import { EPC_TREE_KEY } from '../../config';

type UseTreeLink = [(isDirectory: boolean, itemId: string) => void, Callback];
const DELAY_TO_SCROLL_MS = 300;

export const useTreeLink = (
  entities: Record<string, TreeElement | undefined>,
  treeRef: React.MutableRefObject<null>,
  cabinetId: string,
  handleClose: Callback,
  setIsPending: React.Dispatch<React.SetStateAction<boolean>>,
): UseTreeLink => {
  const { setSelectedNodeId } = useCreateSliceActions(
    newlazyTree.reducers.slice.actions,
  );
  const dispatch = useAppDispatch();

  // Сохраняем controller между рендерами
  const controllerRef = useRef<AbortController | null>(null);

  // Функция для отмены текущего запроса
  const abortRequest = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      controllerRef.current = null;
    }
  }, []);

  const handleLinkClick = useCallback(
    async (isDirectory: boolean, itemId: string): Promise<void> => {
      const item = entities[itemId];

      // Отменяем предыдущий запрос, если он есть
      abortRequest();

      // Создаем новый controller для текущего запроса
      controllerRef.current = new AbortController();

      if (!item) {
        try {
          await dispatch(
            newlazyTree.thunks.getTreeThunk({
              body: {
                ...(isDirectory ? { directoryId: itemId } : { fileId: itemId }),
              },
              queryParams: {
                cabinetId,
                isCabinet: true,
              },
              endpoint: apiUrls.workGroup.EPC.searchLazyTree,
              isSearch: true,
              treeKey: WorkGroupEPCConfig.EPC_TREE_KEY,
              signal: controllerRef.current.signal,
              transformResponse: WorkGroupEPCConfig.transformResponse,
            }),
          ).unwrap();
        } catch (error) {
          if (error === 'Canceled') {
            return;
          }
          throw error;
        }
      }

      setTimeout(
        () => {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          treeRef?.current?.scrollTo?.({ key: itemId });
        },
        item ? 0 : DELAY_TO_SCROLL_MS,
      );

      setSelectedNodeId({ treeKey: EPC_TREE_KEY, nodeId: itemId });
      setIsPending(false);
      handleClose();
    },
    [
      cabinetId,
      dispatch,
      entities,
      handleClose,
      setIsPending,
      setSelectedNodeId,
      treeRef,
      abortRequest,
    ],
  );

  return [handleLinkClick, abortRequest];
};
