/**
 * Тесты для функции generateCatalogByScenario
 * Проверяем корректность расчета totalCountOfLeafs
 */

// Импортируем функцию из mockHandlers.ts (нужно будет экспортировать)
// import { generateCatalogByScenario } from './mockHandlers';

// Временно копируем функцию для тестирования
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

function generateRandomFileName() {
  const extensions = ['.xlsx', '.docx', '.pdf', '.txt', '.jpg', '.png'];
  const names = ['документ', 'файл', 'отчет', 'данные', 'изображение'];
  return names[Math.floor(Math.random() * names.length)] +
         Math.floor(Math.random() * 1000) +
         extensions[Math.floor(Math.random() * extensions.length)];
}

function generateRandomDirectoryName() {
  const names = ['Документы', 'Отчеты', 'Архив', 'Данные', 'Проекты'];
  return names[Math.floor(Math.random() * names.length)] + '_' + Math.floor(Math.random() * 100);
}

// Упрощенная версия функции для тестирования
function generateCatalogByScenario(
  scenario = 'mixed',
  parentId = generateUUID(),
  itemsCount = 10,
  expectedTotalFiles
) {
  console.log(`🧪 ТЕСТ: Генерируем каталог с expectedTotalFiles=${expectedTotalFiles}, itemsCount=${itemsCount}, scenario=${scenario}`);

  const treeData = [];
  let actualItemsCount = itemsCount;

  // Если задано точное количество файлов, корректируем логику
  if (expectedTotalFiles !== undefined) {
    if (expectedTotalFiles === 0) {
      actualItemsCount = Math.floor(Math.random() * 3) + 1; // 1-3 пустых подкаталога
      scenario = 'dirs-only';
    } else {
      switch (scenario) {
        case 'files-only':
          actualItemsCount = expectedTotalFiles;
          break;
        case 'dirs-only':
          actualItemsCount = Math.min(itemsCount, Math.max(1, Math.ceil(expectedTotalFiles / 10)));
          break;
        case 'mixed':
        case 'deep-nested':
        default:
          actualItemsCount = itemsCount;
          break;
      }
    }
  }

  // Рассчитываем оптимальное количество директорий и файлов
  let directoryCount = 0;
  let directFilesCount = 0;

  if (expectedTotalFiles !== undefined) {
    // Точный расчет для достижения expectedTotalFiles
    switch (scenario) {
      case 'files-only':
        directoryCount = 0;
        directFilesCount = actualItemsCount; // Все элементы - файлы
        break;
      case 'dirs-only':
        directoryCount = actualItemsCount; // Все элементы - директории
        directFilesCount = 0;
        break;
      case 'mixed':
      case 'deep-nested':
      default:
        if (expectedTotalFiles === 0) {
          // Пустой каталог: только директории, без файлов
          directoryCount = actualItemsCount;
          directFilesCount = 0;
        } else {
          // Смешанный режим: сбалансированное распределение
          if (expectedTotalFiles <= actualItemsCount) {
            // Если файлов мало, делаем больше прямых файлов
            directFilesCount = Math.max(1, Math.floor(expectedTotalFiles * 0.6)); // 60% файлов - прямые
            directoryCount = Math.min(actualItemsCount - directFilesCount, Math.ceil(expectedTotalFiles * 0.4 / 3)); // Остальные - директории, но не более чем нужно
          } else {
            // Если файлов много, делаем больше директорий
            directFilesCount = Math.min(actualItemsCount, Math.max(1, Math.floor(actualItemsCount * 0.3))); // 30% элементов - прямые файлы
            directoryCount = actualItemsCount - directFilesCount;
          }

          // Проверяем, что у нас есть хотя бы одна директория, если нужно распределить файлы
          const filesForDirectories = expectedTotalFiles - directFilesCount;
          if (filesForDirectories > 0 && directoryCount === 0) {
            // Нужна хотя бы одна директория
            directoryCount = 1;
            directFilesCount = actualItemsCount - 1;
          }
        }
        break;
    }
  } else {
    // Случайное распределение для старой логики
    for (let i = 0; i < actualItemsCount; i++) {
      let isDirectory = Math.random() > 0.5;
      if (isDirectory) {
        directoryCount++;
      } else {
        directFilesCount++;
      }
    }
  }

  console.log(`   Планируется: ${directoryCount} директорий, ${directFilesCount} прямых файлов`);

  // Распределяем файлы
  let filesForDirectories = 0;

  if (expectedTotalFiles !== undefined) {
    if (expectedTotalFiles === 0) {
      filesForDirectories = 0;
    } else if (directoryCount === 0) {
      // Только прямые файлы
      filesForDirectories = 0;
    } else if (directFilesCount === 0) {
      // Только файлы в директориях
      filesForDirectories = expectedTotalFiles;
    } else {
      // Смешанный режим
      filesForDirectories = expectedTotalFiles - directFilesCount;
    }
  }

  console.log(`   Распределение: ${directFilesCount} прямых файлов, ${filesForDirectories} файлов в директориях`);

  // Распределяем файлы по директориям
  const filesPerDirectory = [];
  if (directoryCount > 0) {
    if (expectedTotalFiles === 0) {
      // Специальный случай: все директории должны быть пустыми (0 файлов)
      for (let i = 0; i < directoryCount; i++) {
        filesPerDirectory.push(0);
      }
    } else if (filesForDirectories > 0) {
      // Равномерно распределяем файлы по директориям
      let remaining = filesForDirectories;
      for (let i = 0; i < directoryCount; i++) {
        if (i === directoryCount - 1) {
          // Последняя директория получает все оставшиеся файлы
          filesPerDirectory.push(Math.max(0, remaining));
        } else {
          // Каждая директория получает минимум 1 файл, если файлов достаточно
          const minFiles = remaining > (directoryCount - i) ? 1 : 0;
          const maxFiles = Math.max(minFiles, Math.floor(remaining / (directoryCount - i)));
          const filesForThisDir = Math.min(maxFiles, remaining);
          filesPerDirectory.push(filesForThisDir);
          remaining -= filesForThisDir;
        }
      }
    } else {
      // Если нет файлов для директорий, но есть директории - делаем их пустыми
      for (let i = 0; i < directoryCount; i++) {
        filesPerDirectory.push(0);
      }
    }
  }

  console.log(`   Файлы по директориям: [${filesPerDirectory.join(', ')}]`);

  // Создаем элементы
  let dirIndex = 0;
  let fileIndex = 0;
  let actualDirFiles = 0;

  for (let i = 0; i < actualItemsCount; i++) {
    let isDirectory = false;

    // Строго следуем плану распределения
    if (expectedTotalFiles !== undefined) {
      // Точный контроль: создаем элементы согласно рассчитанному плану
      const remainingDirectories = directoryCount - dirIndex;
      const remainingFiles = directFilesCount - fileIndex;
      const remainingItems = actualItemsCount - i;

      if (remainingDirectories === 0) {
        isDirectory = false; // Только файлы
      } else if (remainingFiles === 0) {
        isDirectory = true; // Только директории
      } else if (remainingItems === remainingDirectories) {
        isDirectory = true; // Нужно создать все оставшиеся директории
      } else if (remainingItems === remainingFiles) {
        isDirectory = false; // Нужно создать все оставшиеся файлы
      } else {
        // Случайный выбор среди допустимых вариантов
        isDirectory = Math.random() > 0.5;
      }
    } else {
      // Старая логика для случайной генерации
      switch (scenario) {
        case 'files-only':
          isDirectory = false;
          break;
        case 'dirs-only':
          isDirectory = true;
          break;
        case 'mixed':
        default:
          isDirectory = Math.random() > 0.5;
          break;
      }
    }

    const itemId = generateUUID();

    if (isDirectory) {
      const totalCountOfLeafs = filesPerDirectory[dirIndex] || 0;
      const isLeaf = totalCountOfLeafs === 0 && Math.random() > 0.5;

      treeData.push({
        itemId,
        title: generateRandomDirectoryName(),
        isDirectory: true,
        totalCountOfLeafs,
        isLeaf,
      });

      actualDirFiles += totalCountOfLeafs;
      dirIndex++;
    } else {
      treeData.push({
        itemId,
        title: generateRandomFileName(),
        isDirectory: false,
      });

      fileIndex++;
    }
  }

  const totalActualFiles = fileIndex + actualDirFiles;

  console.log(`   Результат: ${fileIndex} прямых файлов + ${actualDirFiles} файлов в директориях = ${totalActualFiles} всего`);
  console.log(`   Ожидалось: ${expectedTotalFiles}, получилось: ${totalActualFiles}, совпадение: ${expectedTotalFiles === totalActualFiles ? '✅' : '❌'}`);

  return {
    treeData,
    history: [],
    foundNodes: [],
    debug: {
      expectedTotalFiles,
      actualDirectFiles: fileIndex,
      actualDirFiles,
      totalActualFiles,
      directoryCount,
      directFilesCount,
    }
  };
}

// Тесты
function runTests() {
  console.log('🧪 ЗАПУСК ТЕСТОВ generateCatalogByScenario\n');

  const tests = [
    // Тест 1: Точно 5 файлов, смешанный режим
    { expectedTotalFiles: 5, itemsCount: 8, scenario: 'mixed', name: '5 файлов, смешанный режим' },

    // Тест 2: Точно 10 файлов, смешанный режим
    { expectedTotalFiles: 10, itemsCount: 6, scenario: 'mixed', name: '10 файлов, смешанный режим' },

    // Тест 3: Только файлы
    { expectedTotalFiles: 7, itemsCount: 7, scenario: 'files-only', name: '7 файлов, только файлы' },

    // Тест 4: Только директории
    { expectedTotalFiles: 15, itemsCount: 5, scenario: 'dirs-only', name: '15 файлов в 5 директориях' },

    // Тест 5: Пустой каталог
    { expectedTotalFiles: 0, itemsCount: 3, scenario: 'mixed', name: 'Пустой каталог' },

    // Тест 6: Один файл
    { expectedTotalFiles: 1, itemsCount: 3, scenario: 'mixed', name: 'Один файл' },

    // Дополнительные тесты для крайних случаев
    { expectedTotalFiles: 50, itemsCount: 3, scenario: 'dirs-only', name: '50 файлов в 3 директориях' },
    { expectedTotalFiles: 2, itemsCount: 10, scenario: 'mixed', name: '2 файла, много элементов' },
    { expectedTotalFiles: 100, itemsCount: 1, scenario: 'dirs-only', name: '100 файлов в 1 директории' },
  ];

  let passed = 0;
  let failed = 0;

  tests.forEach((test, index) => {
    console.log(`\n--- ТЕСТ ${index + 1}: ${test.name} ---`);

    const result = generateCatalogByScenario(
      test.scenario,
      generateUUID(),
      test.itemsCount,
      test.expectedTotalFiles
    );

    const success = result.debug.totalActualFiles === test.expectedTotalFiles;

    if (success) {
      console.log(`✅ ПРОЙДЕН`);
      passed++;
    } else {
      console.log(`❌ ПРОВАЛЕН: ожидалось ${test.expectedTotalFiles}, получилось ${result.debug.totalActualFiles}`);
      failed++;
    }
  });

  console.log(`\n🏁 ИТОГИ ТЕСТОВ:`);
  console.log(`✅ Пройдено: ${passed}`);
  console.log(`❌ Провалено: ${failed}`);
  console.log(`📊 Успешность: ${Math.round(passed / (passed + failed) * 100)}%`);

  return { passed, failed };
}

// Запускаем тесты
if (typeof window === 'undefined') {
  // Node.js environment
  runTests();
} else {
  // Browser environment
  window.runCatalogTests = runTests;
  console.log('Тесты загружены. Запустите: runCatalogTests()');
}
