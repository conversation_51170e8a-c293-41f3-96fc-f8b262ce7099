import { PartitionOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd';
import { memo, useCallback } from 'react';
import { LinkedData, WorkGroupEPCConfig } from 'widgets/WorkGroupEPC';
import { newlazyTree } from 'features/NewLazyTree';
import { baseAppInstance, apiUrls } from 'shared/api';
import { appErrorNotification } from 'shared/lib';
import { useAppSelector, useAxiosRequest } from 'shared/model';

interface ViewLinkedButtonProps {
  canEdit: boolean;
  handleUpdateLinked: (body: LinkedData) => void;
  hasLinked: boolean;
  itemId: string;
  title: string;
  togglePopup: (name: keyof typeof WorkGroupEPCConfig.popupsInitial) => void;
}

export const ViewLinkedButton = memo(
  ({
    itemId,
    title,
    hasLinked,
    canEdit,
    handleUpdateLinked,
    togglePopup,
  }: ViewLinkedButtonProps) => {
    const [getLinked] = useAxiosRequest<TreeElement[]>(baseAppInstance);
    const { loadingBranch } = useAppSelector((state) =>
      newlazyTree.selectors.treeStatusesSelector(state, {
        treeKey: WorkGroupEPCConfig.EPC_TREE_KEY,
      }),
    );

    const handleViewLinked = useCallback(
      async (e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation();
        try {
          const linked = await getLinked(
            apiUrls.workGroup.EPC.getLinkedElements(itemId),
          );
          handleUpdateLinked({
            directoryId: itemId || '',
            watchLinked: linked,
            editLinked: linked,
            title,
          });
          togglePopup(!hasLinked && canEdit ? 'edit' : 'watch');
        } catch (error) {
          appErrorNotification(
            'Не удалось получить связанные каталоги и файлы',
            error as AppError,
          );
        }
      },
      [
        getLinked,
        itemId,
        handleUpdateLinked,
        title,
        togglePopup,
        hasLinked,
        canEdit,
      ],
    );

    return (
      <Tooltip
        title={
          loadingBranch
            ? 'Дождитесь окончания загрузки каталогов'
            : hasLinked || !canEdit
            ? 'Просмотр связанных каталогов и файлов'
            : 'Редактирование связей'
        }
      >
        <Button
          type="text"
          icon={<PartitionOutlined />}
          onClick={loadingBranch ? undefined : handleViewLinked}
          style={{ cursor: loadingBranch ? 'not-allowed' : 'pointer' }}
        />
      </Tooltip>
    );
  },
);
